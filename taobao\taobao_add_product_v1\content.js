// 淘宝产品批量添加插件
(function() {
    'use strict';

    // 存储键名
    const STORAGE_KEY = 'taobao_product_add_data';

    // MD5哈希函数实现
    function md5(string) {
        function RotateLeft(lValue, iShiftBits) {
            return (lValue<<iShiftBits) | (lValue>>>(32-iShiftBits));
        }
        function AddUnsigned(lX,lY) {
            var lX4,lY4,lX8,lY8,lResult;
            lX8 = (lX & 0x80000000);
            lY8 = (lY & 0x80000000);
            lX4 = (lX & 0x40000000);
            lY4 = (lY & 0x40000000);
            lResult = (lX & 0x3FFFFFFF)+(lY & 0x3FFFFFFF);
            if (lX4 & lY4) {
                return (lResult ^ 0x80000000 ^ lX8 ^ lY8);
            }
            if (lX4 | lY4) {
                if (lResult & 0x40000000) {
                    return (lResult ^ 0xC0000000 ^ lX8 ^ lY8);
                } else {
                    return (lResult ^ 0x40000000 ^ lX8 ^ lY8);
                }
            } else {
                return (lResult ^ lX8 ^ lY8);
            }
        }
        function F(x,y,z) { return (x & y) | ((~x) & z); }
        function G(x,y,z) { return (x & z) | (y & (~z)); }
        function H(x,y,z) { return (x ^ y ^ z); }
        function I(x,y,z) { return (y ^ (x | (~z))); }
        function FF(a,b,c,d,x,s,ac) {
            a = AddUnsigned(a, AddUnsigned(AddUnsigned(F(b, c, d), x), ac));
            return AddUnsigned(RotateLeft(a, s), b);
        };
        function GG(a,b,c,d,x,s,ac) {
            a = AddUnsigned(a, AddUnsigned(AddUnsigned(G(b, c, d), x), ac));
            return AddUnsigned(RotateLeft(a, s), b);
        };
        function HH(a,b,c,d,x,s,ac) {
            a = AddUnsigned(a, AddUnsigned(AddUnsigned(H(b, c, d), x), ac));
            return AddUnsigned(RotateLeft(a, s), b);
        };
        function II(a,b,c,d,x,s,ac) {
            a = AddUnsigned(a, AddUnsigned(AddUnsigned(I(b, c, d), x), ac));
            return AddUnsigned(RotateLeft(a, s), b);
        };
        function ConvertToWordArray(string) {
            var lWordCount;
            var lMessageLength = string.length;
            var lNumberOfWords_temp1=lMessageLength + 8;
            var lNumberOfWords_temp2=(lNumberOfWords_temp1-(lNumberOfWords_temp1 % 64))/64;
            var lNumberOfWords = (lNumberOfWords_temp2+1)*16;
            var lWordArray=Array(lNumberOfWords-1);
            var lBytePosition = 0;
            var lByteCount = 0;
            while ( lByteCount < lMessageLength ) {
                lWordCount = (lByteCount-(lByteCount % 4))/4;
                lBytePosition = (lByteCount % 4)*8;
                lWordArray[lWordCount] = (lWordArray[lWordCount] | (string.charCodeAt(lByteCount)<<lBytePosition));
                lByteCount++;
            }
            lWordCount = (lByteCount-(lByteCount % 4))/4;
            lBytePosition = (lByteCount % 4)*8;
            lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80<<lBytePosition);
            lWordArray[lNumberOfWords-2] = lMessageLength<<3;
            lWordArray[lNumberOfWords-1] = lMessageLength>>>29;
            return lWordArray;
        };
        function WordToHex(lValue) {
            var WordToHexValue="",WordToHexValue_temp="",lByte,lCount;
            for (lCount = 0;lCount<=3;lCount++) {
                lByte = (lValue>>>(lCount*8)) & 255;
                WordToHexValue_temp = "0" + lByte.toString(16);
                WordToHexValue = WordToHexValue + WordToHexValue_temp.substring(WordToHexValue_temp.length-2);
            }
            return WordToHexValue;
        };
        function Utf8Encode(string) {
            string = string.replace(/\r\n/g,"\n");
            var utftext = "";
            for (var n = 0; n < string.length; n++) {
                var c = string.charCodeAt(n);
                if (c < 128) {
                    utftext += String.fromCharCode(c);
                }
                else if((c > 127) && (c < 2048)) {
                    utftext += String.fromCharCode((c >> 6) | 192);
                    utftext += String.fromCharCode((c & 63) | 128);
                }
                else {
                    utftext += String.fromCharCode((c >> 12) | 224);
                    utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                    utftext += String.fromCharCode((c & 63) | 128);
                }
            }
            return utftext;
        };
        var x=Array();
        var k,AA,BB,CC,DD,a,b,c,d;
        var S11=7, S12=12, S13=17, S14=22;
        var S21=5, S22=9 , S23=14, S24=20;
        var S31=4, S32=11, S33=16, S34=23;
        var S41=6, S42=10, S43=15, S44=21;
        string = Utf8Encode(string);
        x = ConvertToWordArray(string);
        a = 0x67452301; b = 0xEFCDAB89; c = 0x98BADCFE; d = 0x10325476;
        for (k=0;k<x.length;k+=16) {
            AA=a; BB=b; CC=c; DD=d;
            a=FF(a,b,c,d,x[k+0], S11,0xD76AA478);
            d=FF(d,a,b,c,x[k+1], S12,0xE8C7B756);
            c=FF(c,d,a,b,x[k+2], S13,0x242070DB);
            b=FF(b,c,d,a,x[k+3], S14,0xC1BDCEEE);
            a=FF(a,b,c,d,x[k+4], S11,0xF57C0FAF);
            d=FF(d,a,b,c,x[k+5], S12,0x4787C62A);
            c=FF(c,d,a,b,x[k+6], S13,0xA8304613);
            b=FF(b,c,d,a,x[k+7], S14,0xFD469501);
            a=FF(a,b,c,d,x[k+8], S11,0x698098D8);
            d=FF(d,a,b,c,x[k+9], S12,0x8B44F7AF);
            c=FF(c,d,a,b,x[k+10],S13,0xFFFF5BB1);
            b=FF(b,c,d,a,x[k+11],S14,0x895CD7BE);
            a=FF(a,b,c,d,x[k+12],S11,0x6B901122);
            d=FF(d,a,b,c,x[k+13],S12,0xFD987193);
            c=FF(c,d,a,b,x[k+14],S13,0xA679438E);
            b=FF(b,c,d,a,x[k+15],S14,0x49B40821);
            a=GG(a,b,c,d,x[k+1], S21,0xF61E2562);
            d=GG(d,a,b,c,x[k+6], S22,0xC040B340);
            c=GG(c,d,a,b,x[k+11],S23,0x265E5A51);
            b=GG(b,c,d,a,x[k+0], S24,0xE9B6C7AA);
            a=GG(a,b,c,d,x[k+5], S21,0xD62F105D);
            d=GG(d,a,b,c,x[k+10],S22,0x2441453);
            c=GG(c,d,a,b,x[k+15],S23,0xD8A1E681);
            b=GG(b,c,d,a,x[k+4], S24,0xE7D3FBC8);
            a=GG(a,b,c,d,x[k+9], S21,0x21E1CDE6);
            d=GG(d,a,b,c,x[k+14],S22,0xC33707D6);
            c=GG(c,d,a,b,x[k+3], S23,0xF4D50D87);
            b=GG(b,c,d,a,x[k+8], S24,0x455A14ED);
            a=GG(a,b,c,d,x[k+13],S21,0xA9E3E905);
            d=GG(d,a,b,c,x[k+2], S22,0xFCEFA3F8);
            c=GG(c,d,a,b,x[k+7], S23,0x676F02D9);
            b=GG(b,c,d,a,x[k+12],S24,0x8D2A4C8A);
            a=HH(a,b,c,d,x[k+5], S31,0xFFFA3942);
            d=HH(d,a,b,c,x[k+8], S32,0x8771F681);
            c=HH(c,d,a,b,x[k+11],S33,0x6D9D6122);
            b=HH(b,c,d,a,x[k+14],S34,0xFDE5380C);
            a=HH(a,b,c,d,x[k+1], S31,0xA4BEEA44);
            d=HH(d,a,b,c,x[k+4], S32,0x4BDECFA9);
            c=HH(c,d,a,b,x[k+7], S33,0xF6BB4B60);
            b=HH(b,c,d,a,x[k+10],S34,0xBEBFBC70);
            a=HH(a,b,c,d,x[k+13],S31,0x289B7EC6);
            d=HH(d,a,b,c,x[k+0], S32,0xEAA127FA);
            c=HH(c,d,a,b,x[k+3], S33,0xD4EF3085);
            b=HH(b,c,d,a,x[k+6], S34,0x4881D05);
            a=HH(a,b,c,d,x[k+9], S31,0xD9D4D039);
            d=HH(d,a,b,c,x[k+12],S32,0xE6DB99E5);
            c=HH(c,d,a,b,x[k+15],S33,0x1FA27CF8);
            b=HH(b,c,d,a,x[k+2], S34,0xC4AC5665);
            a=II(a,b,c,d,x[k+0], S41,0xF4292244);
            d=II(d,a,b,c,x[k+7], S42,0x432AFF97);
            c=II(c,d,a,b,x[k+14],S43,0xAB9423A7);
            b=II(b,c,d,a,x[k+5], S44,0xFC93A039);
            a=II(a,b,c,d,x[k+12],S41,0x655B59C3);
            d=II(d,a,b,c,x[k+3], S42,0x8F0CCC92);
            c=II(c,d,a,b,x[k+10],S43,0xFFEFF47D);
            b=II(b,c,d,a,x[k+1], S44,0x85845DD1);
            a=II(a,b,c,d,x[k+8], S41,0x6FA87E4F);
            d=II(d,a,b,c,x[k+15],S42,0xFE2CE6E0);
            c=II(c,d,a,b,x[k+6], S43,0xA3014314);
            b=II(b,c,d,a,x[k+13],S44,0x4E0811A1);
            a=II(a,b,c,d,x[k+4], S41,0xF7537E82);
            d=II(d,a,b,c,x[k+11],S42,0xBD3AF235);
            c=II(c,d,a,b,x[k+2], S43,0x2AD7D2BB);
            b=II(b,c,d,a,x[k+9], S44,0xEB86D391);
            a=AddUnsigned(a,AA);
            b=AddUnsigned(b,BB);
            c=AddUnsigned(c,CC);
            d=AddUnsigned(d,DD);
        }
        return (WordToHex(a)+WordToHex(b)+WordToHex(c)+WordToHex(d)).toLowerCase();
    }

    // 获取h5_tk token用于签名
    function getH5Token() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === '_m_h5_tk') {
                return value.split('_')[0];
            }
        }
        return "";
    }

    // 从URL获取liveId
    function getLiveIdFromUrl() {
        // 首先尝试从当前页面URL获取
        const urlParams = new URLSearchParams(window.location.search);
        let liveId = urlParams.get('liveId');

        // 如果当前页面没有，尝试从其他可能的URL格式获取
        if (!liveId) {
            const match = window.location.href.match(/liveId[=\/](\d+)/);
            if (match) {
                liveId = match[1];
            }
        }

        // 如果还是没有，使用默认值
        return liveId || "525328079529";
    }

    // 初始化插件
    function initPlugin() {
        createPluginModal();
        loadStoredData();
    }

    // 创建插件Modal
    function createPluginModal() {
        // 获取保存的位置
        const savedPosition = getSavedPosition();

        // 创建modal容器
        const modal = document.createElement('div');
        modal.id = 'taobao-plugin-modal';
        modal.innerHTML = `
            <div class="plugin-header" id="plugin-header">
                <span class="plugin-title">
                    <span class="drag-icon">⋮⋮</span>
                    淘宝产品批量添加
                </span>
                <div class="plugin-stats">
                    <span id="product-count">已添加: 0 个商品</span>
                    <span id="success-count">成功: 0</span>
                    <span id="fail-count">失败: 0</span>
                </div>
            </div>
            <div class="plugin-content">
                <div class="input-section">
                    <div class="input-header">
                        <label for="product-ids">产品ID列表（一行一个）：</label>
                        <button id="clear-input-btn" class="clear-input-btn">清空输入</button>
                    </div>
                    <textarea id="product-ids" placeholder="请输入产品ID，一行一个" style="min-height: 120px; resize: vertical;"></textarea>
                </div>
                <div class="plugin-buttons">
                    <button id="add-btn" class="plugin-btn add-btn">批量添加</button>
                    <button id="export-btn" class="plugin-btn export-btn">导出Excel</button>
                    <button id="clear-btn" class="plugin-btn clear-btn">清空数据</button>
                </div>
            </div>
        `;

        // 应用保存的位置
        modal.style.position = 'fixed';
        modal.style.left = savedPosition.left;
        modal.style.top = savedPosition.top;
        modal.style.transform = 'none'; // 取消居中变换

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            #taobao-plugin-modal {
                width: 600px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                z-index: 999999;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                color: white;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                user-select: none;
                transition: box-shadow 0.3s ease;
            }

            #taobao-plugin-modal.dragging {
                box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);
                transform: scale(1.02);
            }

            .plugin-header {
                padding: 15px 20px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: move;
                position: relative;
            }

            .plugin-header:hover {
                background: rgba(255, 255, 255, 0.1);
            }

            .plugin-title {
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .drag-icon {
                font-size: 14px;
                opacity: 0.7;
                transform: rotate(90deg);
                transition: opacity 0.3s ease;
            }

            .plugin-header:hover .drag-icon {
                opacity: 1;
            }

            .plugin-stats {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                gap: 4px;
            }

            .plugin-stats > div, .plugin-stats > span {
                font-size: 11px;
                background: rgba(255, 255, 255, 0.2);
                padding: 3px 10px;
                border-radius: 15px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }

            .plugin-content {
                padding: 20px;
            }

            .input-section {
                margin-bottom: 20px;
            }

            .input-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
            }

            .input-header label {
                font-size: 14px;
                font-weight: 500;
            }

            .clear-input-btn {
                background: rgba(255, 255, 255, 0.2);
                border: none;
                color: white;
                padding: 4px 12px;
                border-radius: 4px;
                font-size: 12px;
                cursor: pointer;
                transition: background 0.3s ease;
            }

            .clear-input-btn:hover {
                background: rgba(255, 255, 255, 0.3);
            }

            #product-ids {
                width: 100%;
                min-height: 120px;
                padding: 12px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.1);
                color: white;
                font-size: 14px;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                resize: vertical;
                box-sizing: border-box;
            }

            #product-ids::placeholder {
                color: rgba(255, 255, 255, 0.6);
            }

            #product-ids:focus {
                outline: none;
                border-color: rgba(255, 255, 255, 0.5);
                background: rgba(255, 255, 255, 0.15);
            }

            .plugin-buttons {
                display: flex;
                gap: 12px;
            }

            .plugin-btn {
                flex: 1;
                padding: 10px 16px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }

            .add-btn {
                background: linear-gradient(135deg, #4CAF50, #45a049);
                color: white;
            }

            .export-btn {
                background: linear-gradient(135deg, #2196F3, #1976D2);
                color: white;
            }

            .clear-btn {
                background: linear-gradient(135deg, #f44336, #d32f2f);
                color: white;
            }

            .plugin-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }

            .plugin-btn:active {
                transform: translateY(0);
            }

            .plugin-btn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none;
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(modal);

        // 初始化拖动功能
        initDragFunctionality(modal);

        // 绑定事件
        bindEvents();
    }

    // 获取保存的位置
    function getSavedPosition() {
        try {
            const saved = localStorage.getItem('taobao_plugin_position');
            if (saved) {
                const position = JSON.parse(saved);
                // 验证位置是否在屏幕范围内
                const maxX = window.innerWidth - 600; // 减去插件宽度
                const maxY = window.innerHeight - 300; // 减去插件高度的估计值

                return {
                    left: Math.max(0, Math.min(position.left, maxX)) + 'px',
                    top: Math.max(0, Math.min(position.top, maxY)) + 'px'
                };
            }
        } catch (error) {
            console.error('获取保存位置失败:', error);
        }

        // 默认位置（顶部中间）
        return {
            left: Math.max(0, (window.innerWidth - 600) / 2) + 'px',
            top: '20px'
        };
    }
    
    // 保存位置
    function savePosition(left, top) {
        try {
            const position = {
                left: parseInt(left),
                top: parseInt(top)
            };
            localStorage.setItem('taobao_plugin_position', JSON.stringify(position));
        } catch (error) {
            console.error('保存位置失败:', error);
        }
    }
    
    // 初始化拖动功能
    function initDragFunctionality(modal) {
        const header = modal.querySelector('#plugin-header');
        let isDragging = false;
        let startX, startY, initialLeft, initialTop;
        
        header.addEventListener('mousedown', function(e) {
            isDragging = true;
            modal.classList.add('dragging');
            
            startX = e.clientX;
            startY = e.clientY;
            
            const rect = modal.getBoundingClientRect();
            initialLeft = rect.left;
            initialTop = rect.top;
            
            // 防止文本选择
            e.preventDefault();
            
            // 添加全局鼠标事件监听器
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
        });
        
        function onMouseMove(e) {
            if (!isDragging) return;
            
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            
            let newLeft = initialLeft + deltaX;
            let newTop = initialTop + deltaY;
            
            // 限制在屏幕范围内
            const maxLeft = window.innerWidth - modal.offsetWidth;
            const maxTop = window.innerHeight - modal.offsetHeight;
            
            newLeft = Math.max(0, Math.min(newLeft, maxLeft));
            newTop = Math.max(0, Math.min(newTop, maxTop));
            
            modal.style.left = newLeft + 'px';
            modal.style.top = newTop + 'px';
        }
        
        function onMouseUp() {
            if (isDragging) {
                isDragging = false;
                modal.classList.remove('dragging');
                
                // 保存新位置
                const rect = modal.getBoundingClientRect();
                savePosition(rect.left, rect.top);
                
                // 移除全局事件监听器
                document.removeEventListener('mousemove', onMouseMove);
                document.removeEventListener('mouseup', onMouseUp);
            }
        }
        
        // 处理窗口大小变化
        window.addEventListener('resize', function() {
            const rect = modal.getBoundingClientRect();
            const maxLeft = window.innerWidth - modal.offsetWidth;
            const maxTop = window.innerHeight - modal.offsetHeight;
            
            if (rect.left > maxLeft || rect.top > maxTop) {
                const newLeft = Math.max(0, Math.min(rect.left, maxLeft));
                const newTop = Math.max(0, Math.min(rect.top, maxTop));
                
                modal.style.left = newLeft + 'px';
                modal.style.top = newTop + 'px';
                
                savePosition(newLeft, newTop);
            }
        });
    }

    // 绑定事件
    function bindEvents() {
        document.getElementById('add-btn').addEventListener('click', addProducts);
        document.getElementById('export-btn').addEventListener('click', exportData);
        document.getElementById('clear-btn').addEventListener('click', clearData);
        document.getElementById('clear-input-btn').addEventListener('click', clearInput);
    }

    // 清空输入框
    function clearInput() {
        document.getElementById('product-ids').value = '';
    }

    // 批量添加产品
    async function addProducts() {
        const textarea = document.getElementById('product-ids');
        const productIds = textarea.value.trim().split('\n').filter(id => id.trim()).map(id => id.trim());

        if (productIds.length === 0) {
            layer.msg('请输入产品ID');
            return;
        }

        showLoading('正在批量添加产品...');

        try {
            // 分批处理，每次最多200个
            const batchSize = 200;
            let totalSuccess = 0;
            let totalFail = 0;
            const allResults = [];

            for (let i = 0; i < productIds.length; i += batchSize) {
                const batchIds = productIds.slice(i, i + batchSize);
                console.log(`处理第 ${Math.floor(i/batchSize) + 1} 批，共 ${batchIds.length} 个产品`);

                const batchResult = await addProductBatch(batchIds);
                allResults.push(...batchResult.results);
                totalSuccess += batchResult.successCount;
                totalFail += batchResult.failCount;

                // 更新统计显示
                updateStats(totalSuccess + totalFail, totalSuccess, totalFail);

                // 批次间等待
                if (i + batchSize < productIds.length) {
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }

            // 保存结果
            saveToLocalStorage(allResults);
            layer.msg(`添加完成！成功: ${totalSuccess} 个，失败: ${totalFail} 个`);

        } catch (error) {
            console.error('添加失败:', error);
            layer.msg('添加失败: ' + error.message);
        } finally {
            hideLoading();
        }
    }

    // 添加产品批次
    async function addProductBatch(productIds) {
        const timestamp = Date.now();
        const appKey = '12574478';
        const api = 'mtop.mediaplatform.video.additem.batch';

        // 获取liveId
        const liveId = getLiveIdFromUrl();

        // 构建批量参数
        const batchParams = productIds.map(itemId => ({
            "itemId": parseInt(itemId),
            "publishParam": {
                "right": "",
                "rightType": "1",
                "benefitCodes": "[]",
                "benefitDesc": "",
                "itemExtendVal": "{}",
                "pageSource": "",
                "sign": "",
                "tabType": "9",
                "itemCategoryIdList": "[]"
            }
        }));

        const dataStr = JSON.stringify({
            "liveId": liveId,
            "batchParams": JSON.stringify(batchParams)
        });

        // 获取h5_tk token
        const h5Token = getH5Token();
        if (!h5Token) {
            throw new Error('未找到h5_tk token，请确保已登录淘宝');
        }

        // 生成签名
        const signString = `${h5Token}&${timestamp}&${appKey}&${dataStr}`;
        const sign = md5(signString);

        console.log('签名字符串:', signString);
        console.log('生成的签名:', sign);

        // 构建请求参数
        const requestData = `data=${encodeURIComponent(dataStr)}`;

        const requestUrl = `https://h5api.m.taobao.com/h5/mtop.mediaplatform.video.additem.batch/1.0/?jsv=2.7.2&appKey=${appKey}&t=${timestamp}&sign=${sign}&api=${api}&v=1.0&type=originaljson&dataType=json&preventFallback=true`;

        try {
            const response = await fetch(requestUrl, {
                method: 'POST',
                headers: {
                    'accept': 'application/json',
                    'accept-language': 'zh-CN,zh;q=0.9',
                    'content-type': 'application/x-www-form-urlencoded',
                    'priority': 'u=1, i',
                    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                    'sec-ch-ua-mobile': '?0',
                    'sec-ch-ua-platform': '"Windows"',
                    'sec-fetch-dest': 'empty',
                    'sec-fetch-mode': 'cors',
                    'sec-fetch-site': 'same-site'
                },
                referrer: 'https://market.m.taobao.com/',
                referrerPolicy: 'strict-origin-when-cross-origin',
                body: requestData,
                mode: 'cors',
                credentials: 'include'
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            return processAddResponse(data);

        } catch (error) {
            console.error('批次添加失败:', error);
            throw error;
        }
    }

    // 处理添加响应
    function processAddResponse(data) {
        const results = [];
        let successCount = 0;
        let failCount = 0;

        if (data && data.data && data.data.result) {
            data.data.result.forEach(item => {
                const productData = {
                    itemId: item.itemId,
                    isSuccess: item.isSuccess,
                    errorCode: item.errorCode,
                    msgInfo: item.msgInfo || '',
                    addTime: new Date().toLocaleString()
                };

                results.push(productData);

                if (item.isSuccess) {
                    successCount++;
                } else {
                    failCount++;
                }
            });
        }

        return {
            results: results,
            successCount: successCount,
            failCount: failCount
        };
    }

    // 更新统计显示
    function updateStats(total, success, fail) {
        document.getElementById('product-count').textContent = `已添加: ${total} 个商品`;
        document.getElementById('success-count').textContent = `成功: ${success}`;
        document.getElementById('fail-count').textContent = `失败: ${fail}`;
    }



    // 保存到本地存储
    function saveToLocalStorage(newData) {
        try {
            const existingData = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');

            // 合并数据（避免重复）
            const allData = [...existingData];
            newData.forEach(newItem => {
                const exists = allData.find(item => item.itemId === newItem.itemId);
                if (!exists) {
                    allData.push(newItem);
                } else {
                    // 更新现有数据
                    const index = allData.findIndex(item => item.itemId === newItem.itemId);
                    allData[index] = newItem;
                }
            });

            localStorage.setItem(STORAGE_KEY, JSON.stringify(allData));
            updateProductCount();
        } catch (error) {
            console.error('保存失败:', error);
            layer.msg('保存失败: ' + error.message);
        }
    }

    // 导出数据
    function exportData() {
        try {
            const data = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');

            if (data.length === 0) {
                layer.msg('暂无数据可导出', {icon: 3});
                return;
            }

            // 分类数据
            const successData = data.filter(item => item.isSuccess);
            const failData = data.filter(item => !item.isSuccess);

            // 创建工作簿
            const wb = XLSX.utils.book_new();

            // 创建成功添加的工作表
            if (successData.length > 0) {
                const successExportData = successData.map(item => ({
                    '商品ID': String(item.itemId),
                    '添加状态': '成功',
                    '错误代码': item.errorCode,
                    '消息信息': item.msgInfo,
                    '添加时间': item.addTime,
                    '链接': `https://item.taobao.com/item.htm?id=${item.itemId}`
                }));

                const ws1 = XLSX.utils.json_to_sheet(successExportData);
                ws1['!cols'] = [
                    {wch: 15}, {wch: 12}, {wch: 15}, {wch: 30}, {wch: 20}, {wch: 15}
                ];
                XLSX.utils.book_append_sheet(wb, ws1, '添加成功');
            }

            // 创建失败添加的工作表
            if (failData.length > 0) {
                const failExportData = failData.map(item => ({
                    '商品ID': String(item.itemId),
                    '添加状态': '失败',
                    '错误代码': item.errorCode,
                    '消息信息': item.msgInfo,
                    '添加时间': item.addTime,
                    '链接': `https://item.taobao.com/item.htm?id=${item.itemId}`
                }));

                const ws2 = XLSX.utils.json_to_sheet(failExportData);
                ws2['!cols'] = [
                    {wch: 15}, {wch: 12}, {wch: 15}, {wch: 30}, {wch: 20}, {wch: 15}
                ];
                XLSX.utils.book_append_sheet(wb, ws2, '添加失败');
            }

            // 导出文件
            const fileName = `淘宝产品批量添加结果_${new Date().getTime()}.xlsx`;
            XLSX.writeFile(wb, fileName);

            layer.msg(`成功导出数据 - 成功: ${successData.length}个, 失败: ${failData.length}个`);

        } catch (error) {
            console.error('导出失败:', error);
            layer.msg('导出失败: ' + error.message);
        }
    }

    // 清空数据
    function clearData() {
        layer.confirm('确定要清空所有数据吗？', {
            btn: ['确定', '取消'],
            icon: 3,
            title: '确认清空'
        }, function(index) {
            localStorage.removeItem(STORAGE_KEY);
            updateProductCount();
            layer.msg('数据已清空');
            layer.close(index);
        });
    }

    // 显示加载状态
    function showLoading(text = '加载中...') {
        layer.load(2, {
            shade: [0.3, '#000'],
            content: text,
            success: function(layero) {
                // 确保加载层显示在整个页面中央，并设置文字样式
                layero.css({
                    'position': 'fixed',
                    'top': '50%',
                    'left': '50%',
                    'transform': 'translate(-50%, -50%)',
                    'z-index': '19891015'
                });
                // 设置文字水平显示
                layero.find('.layui-layer-content').css({
                    'writing-mode': 'horizontal-tb',
                    'text-align': 'center',
                    'white-space': 'nowrap',
                    'font-size': '14px',
                    'color': '#666'
                });
            }
        });
    }

    // 隐藏加载状态
    function hideLoading() {
        layer.closeAll('loading');
    }

    // 更新商品数量显示
    function updateProductCount() {
        try {
            const data = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');
            document.getElementById('product-count').textContent = `已检查: ${data.length} 个商品`;
        } catch (error) {
            console.error('更新计数失败:', error);
        }
    }

    // 加载已存储的数据
    function loadStoredData() {
        updateProductCount();
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initPlugin);
    } else {
        initPlugin();
    }

})();
