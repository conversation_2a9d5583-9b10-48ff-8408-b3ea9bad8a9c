<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>淘宝产品批量添加测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #666;
        }
        pre {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .success {
            color: #4CAF50;
            font-weight: bold;
        }
        .fail {
            color: #f44336;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>淘宝产品批量添加功能测试</h1>
        
        <div class="test-section">
            <h3>1. 数据解码测试</h3>
            <p>原始数据格式：</p>
            <pre id="original-data"></pre>
            <p>解码后的数据：</p>
            <pre id="decoded-data"></pre>
        </div>

        <div class="test-section">
            <h3>2. 响应处理测试</h3>
            <p>模拟API响应：</p>
            <pre id="response-data"></pre>
            <p>处理结果：</p>
            <div id="process-result"></div>
        </div>

        <div class="test-section">
            <h3>3. 功能说明</h3>
            <ul>
                <li>支持批量添加产品，每批最多200个</li>
                <li>自动统计成功和失败数量</li>
                <li>支持导出Excel报告</li>
                <li>数据本地存储，防止丢失</li>
            </ul>
        </div>
    </div>

    <script>
        // 测试数据解码
        function testDataDecoding() {
            const originalData = `data=%7B%22liveId%22%3A%22525498798489%22%2C%22batchParams%22%3A%22%5B%7B%5C%22itemId%5C%22%3A769009396020%2C%5C%22publishParam%5C%22%3A%7B%5C%22right%5C%22%3A%5C%22%5C%22%2C%5C%22rightType%5C%22%3A%5C%221%5C%22%2C%5C%22benefitCodes%5C%22%3A%5C%22%5B%5D%5C%22%2C%5C%22benefitDesc%5C%22%3A%5C%22%5C%22%2C%5C%22itemExtendVal%5C%22%3A%5C%22%7B%7D%5C%22%2C%5C%22pageSource%5C%22%3A%5C%22%5C%22%2C%5C%22sign%5C%22%3A%5C%22%5C%22%2C%5C%22tabType%5C%22%3A%5C%229%5C%22%2C%5C%22itemCategoryIdList%5C%22%3A%5C%22%5B%5D%5C%22%7D%7D%5D%22%7D`;
            
            document.getElementById('original-data').textContent = originalData.substring(0, 200) + '...';
            
            // 解码URL编码
            const decodedData = decodeURIComponent(originalData.replace('data=', ''));
            const parsedData = JSON.parse(decodedData);
            
            document.getElementById('decoded-data').textContent = JSON.stringify(parsedData, null, 2);
        }

        // 测试响应处理
        function testResponseProcessing() {
            const mockResponse = {
                "api": "mtop.mediaplatform.video.additem.batch",
                "data": {
                    "result": [
                        {
                            "errorCode": "SUCCESS",
                            "isSuccess": true,
                            "itemId": 769009396020,
                            "msgInfo": ""
                        },
                        {
                            "errorCode": "ITEM_NOT_FOUND",
                            "isSuccess": false,
                            "itemId": 123456789,
                            "msgInfo": "商品不存在"
                        }
                    ]
                },
                "ret": ["SUCCESS::调用成功"],
                "traceId": "test123456",
                "v": "1.0"
            };

            document.getElementById('response-data').textContent = JSON.stringify(mockResponse, null, 2);

            // 模拟处理响应
            let successCount = 0;
            let failCount = 0;
            const results = [];

            if (mockResponse.data && mockResponse.data.result) {
                mockResponse.data.result.forEach(item => {
                    results.push({
                        itemId: item.itemId,
                        isSuccess: item.isSuccess,
                        errorCode: item.errorCode,
                        msgInfo: item.msgInfo
                    });
                    
                    if (item.isSuccess) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                });
            }

            const resultHtml = `
                <p><span class="success">成功: ${successCount} 个</span></p>
                <p><span class="fail">失败: ${failCount} 个</span></p>
                <p>详细结果:</p>
                <pre>${JSON.stringify(results, null, 2)}</pre>
            `;
            
            document.getElementById('process-result').innerHTML = resultHtml;
        }

        // 页面加载完成后执行测试
        document.addEventListener('DOMContentLoaded', function() {
            testDataDecoding();
            testResponseProcessing();
        });
    </script>
</body>
</html>
